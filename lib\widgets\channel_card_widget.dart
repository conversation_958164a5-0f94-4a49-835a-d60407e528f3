import 'package:flutter/material.dart';
import 'package:country_flags/country_flags.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/utils/display_mode.dart';
import 'package:cat_tv/utils/image_cache_config.dart';

class ChannelCardWidget extends StatelessWidget {
  final Channel channel;
  final DisplayMode mode;
  final Future<void> Function(BuildContext, Channel) onChannelTap;
  final VoidCallback onToggleFavorite;

  const ChannelCardWidget({
    super.key,
    required this.channel,
    required this.mode,
    required this.onChannelTap,
    required this.onToggleFavorite,
  });

  // Cache computed values to avoid recalculation
  static const double _listImageSize = 50.0;
  static const double _gridImageSize = 40.0;
  static const double _listPadding = 8.0;
  static const double _gridPadding = 4.0;
  static const double _listBorderRadius = 12.0;
  static const double _gridBorderRadius = 8.0;

  @override
  Widget build(BuildContext context) {
    final bool isList = mode == DisplayMode.list;
    final bool isSmallTiles = mode == DisplayMode.smallTiles;
    final double imageSize = isList ? _listImageSize : _gridImageSize;
    final double fontSize = isList ? 14 : 10;
    final double favoriteIconSize = isList ? 24 : 20;
    final double padding = isList ? _listPadding : _gridPadding;
    final double borderRadius = isList ? _listBorderRadius : _gridBorderRadius;

    // Optimized widget structure with less nesting
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(borderRadius),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1.0,
          ),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () async {
            await onChannelTap(context, channel);
          },
          child: Padding(
            padding: EdgeInsets.all(padding),
            child:
                isList
                    ? Row(
                      children: [
                        channel.logoUrl != null && channel.logoUrl!.isNotEmpty
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: ImageCacheConfig.buildOptimizedImage(
                                imageUrl: channel.logoUrl!,
                                width: imageSize,
                                height: imageSize,
                                fit: BoxFit.contain,
                                placeholder: Icon(
                                  Icons.tv,
                                  size: imageSize,
                                  color: Colors.grey,
                                ),
                                errorWidget: Icon(
                                  Icons.image_not_supported,
                                  size: imageSize,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                            : Icon(
                              Icons.tv,
                              size: imageSize,
                              color: Colors.grey,
                            ),
                        const SizedBox(width: 8.0),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                channel.name,
                                style: TextStyle(
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                children: [
                                  CountryFlag.fromCountryCode(
                                    channel.countryCode,
                                    height: fontSize * 1.2,
                                    width: fontSize * 1.6,
                                    borderRadius: 4,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    channel.countryCode,
                                    style: TextStyle(
                                      fontSize: fontSize * 0.8,
                                      color: Colors.white70,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(width: 8),
                                ],
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            channel.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                channel.isFavorite
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.white70,
                            size: favoriteIconSize,
                          ),
                          onPressed: onToggleFavorite,
                        ),
                      ],
                    )
                    : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        channel.logoUrl != null && channel.logoUrl!.isNotEmpty
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: ImageCacheConfig.buildOptimizedImage(
                                imageUrl: channel.logoUrl!,
                                width: imageSize,
                                height: imageSize,
                                fit: BoxFit.contain,
                                placeholder: Icon(
                                  Icons.tv,
                                  size: imageSize,
                                  color: Colors.grey,
                                ),
                                errorWidget: Icon(
                                  Icons.image_not_supported,
                                  size: imageSize,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                            : Icon(
                              Icons.tv,
                              size: imageSize,
                              color: Colors.grey,
                            ),
                        SizedBox(height: isSmallTiles ? 4.0 : 8.0),
                        Text(
                          channel.name,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: fontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (isList) ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CountryFlag.fromCountryCode(
                                channel.countryCode,
                                height: fontSize * 1.2,
                                width: fontSize * 1.6,
                                borderRadius: 4,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                channel.countryCode,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: fontSize * 0.8,
                                  color: Colors.white70,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                        ],
                        IconButton(
                          icon: Icon(
                            channel.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                channel.isFavorite
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.white70,
                            size: favoriteIconSize,
                          ),
                          onPressed: onToggleFavorite,
                        ),
                      ],
                    ),
          ),
        ),
      ),
    );
  }
}
