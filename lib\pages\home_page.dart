// ignore_for_file: use_build_context_synchronously

import 'package:cat_tv/pages/player_page.dart';
import 'dart:ui'; // Required for ImageFilter
import 'dart:io' show Platform; // Import for platform check
import 'package:flutter/material.dart';
// Import for CachedNetworkImage
import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:cat_tv/repositories/region_repository.dart';
import 'package:cat_tv/repositories/country_repository.dart';
import 'package:cat_tv/repositories/language_repository.dart';
import 'package:cat_tv/repositories/category_repository.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
// Import for country flags
import 'package:cat_tv/pages/fixtures_list_page.dart';
import 'package:cat_tv/widgets/filter_widget.dart';
import 'package:cat_tv/controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/pages/webview_page.dart';
import 'package:cat_tv/pages/favorites_page.dart';
import 'package:cat_tv/pages/settings_page.dart';
import 'package:cat_tv/pages/ad_page.dart'; // Import AdPage
import 'package:cat_tv/l10n/app_localizations.dart';
import 'package:cat_tv/widgets/channel_skeleton_loader.dart'; // Import the skeleton loader
import 'package:cat_tv/widgets/channel_card_widget.dart'; // Import the new ChannelCardWidget
import 'package:cat_tv/services/ad_manager.dart'; // Import AdManager
import 'package:cat_tv/utils/display_mode.dart'; // Import the common DisplayMode enum
import 'package:cat_tv/pages/iptv_webview_page.dart'; // Import IptvWebViewFetcher
import 'package:cat_tv/services/iptv_data_service.dart'; // Import IptvDataService
import 'package:cat_tv/utils/mobile_scroll_behavior.dart'; // Import optimized scroll behavior
import 'dart:async'; // For Timer

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  DisplayMode _displayMode = DisplayMode.list; // Default display mode
  final List<Channel> _channels = [];
  final AdManager _adManager = AdManager(); // Initialize AdManager
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentOffset = 0;
  final int _pageSize = 20;
  late ChannelRepository _repo;
  late Database _db;
  final OptimizedScrollController _scrollController =
      OptimizedScrollController();
  final filter_ctrl.FilterController _filterController =
      filter_ctrl.FilterController();
  List<Region> _regions = [];
  List<Country> _countries = [];
  List<Language> _languages = [];
  List<cat_model.Category> _categories = [];
  bool _isIptvUpdating = false;
  String _iptvUpdateStatus = '';
  DateTime? _currentExpirationDate; // Use this for the actual expiration date
  final IptvDataService _iptvDataService = IptvDataService();
  Timer? _scrollDebounceTimer; // For debouncing scroll events
  bool _showFilters = false; // New state variable for filter visibility

  double get _filterAppBarHeight {
    double height =
        0.0; // Toolbar height is now 0, only account for FilterWidget
    if (_showFilters) {
      height += 200.0; // Height of the FilterWidget when visible
    }
    return height;
  }

  @override
  void initState() {
    super.initState();
    // Initialize
    _initDbAndLoad();
    _scrollController.addListener(_onScroll);
    _filterController.addListener(() {
      _loadMoreChannels(reset: true);
    });
    // Set _showFilters to true by default for desktop platforms
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      _showFilters = true;
    }
  }

  Future<void> _initDbAndLoad() async {
    setState(() => _isLoading = true);
    _db = await DatabaseLoader.openPrebuiltDatabase();
    _repo = ChannelRepository(_db);
    await _loadFilterData();
    await _loadMoreChannels(reset: true);
    await _loadIptvStatusAndExpiration(); // Load status and expiration date
    setState(() => _isLoading = false);

    // No automatic fetch on init based on new requirements.
    // The manual button will handle the update logic.
  }

  Future<void> _loadFilterData() async {
    final regionRepo = RegionRepository(_db);
    final countryRepo = CountryRepository(_db);
    final languageRepo = LanguageRepository(_db);
    final categoryRepo = CategoryRepository(_db);
    final regions = await regionRepo.getAllRegions();
    final countries = await countryRepo.getAllCountries();
    final languages = await languageRepo.getAllLanguages();
    final categories = await categoryRepo.getAllCategories();
    setState(() {
      _regions = regions;
      _countries = countries;
      _languages = languages;
      _categories = categories.cast<cat_model.Category>();
    });
  }

  Future<void> _loadMoreChannels({bool reset = false}) async {
    if (reset) {
      setState(() {
        _channels.clear();
        _currentOffset = 0;
        _hasMore = true;
      });
    }
    setState(() => _isLoading = true);
    if (kDebugMode) {
      print(
        'Loading channels: offset=$_currentOffset, limit=$_pageSize, filter=${_filterController.filter}',
      );
    }
    try {
      final newChannels = await _repo.getChannelsPaged(
        limit: _pageSize,
        offset: _currentOffset,
        filter: _filterController.filter,
      );
      if (kDebugMode) print('Loaded ${newChannels.length} channels');
      setState(() {
        _channels.addAll(newChannels);
        _currentOffset += newChannels.length;
        _hasMore = newChannels.length == _pageSize;
        _isLoading = false;
      });
    } catch (e, st) {
      if (kDebugMode) {
        print('Error loading channels: $e');
        print(st);
      }
      setState(() => _isLoading = false);
    }
  }

  void _onScroll() {
    // Debounce scroll events to improve performance
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      // Use optimized scroll controller method
      if (_scrollController.isNearBottom() && !_isLoading && _hasMore) {
        _loadMoreChannels();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollDebounceTimer?.cancel(); // Clean up debounce timer
    // The database is now a singleton managed by DatabaseLoader,
    // so it should not be closed here.
    super.dispose();
  }

  Widget _buildChannelList() {
    if (_channels.isEmpty && _isLoading) {
      return ChannelSkeletonLoader(displayMode: _displayMode);
    }

    final Widget loadingIndicator = Padding(
      padding: const EdgeInsets.all(16.0),
      child: Center(child: CircularProgressIndicator()),
    );

    // Optimized scroll physics for better mobile performance
    final ScrollPhysics scrollPhysics =
        kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS
            ? const ClampingScrollPhysics()
            : const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            );

    Widget currentView;
    if (_displayMode == DisplayMode.list) {
      currentView = ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          overscroll: false, // Disable glow/overscroll on mobile
        ),
        child: ListView.separated(
          key: const ValueKey('listView'), // Key for AnimatedSwitcher
          controller: _scrollController,
          padding: const EdgeInsets.all(8.0),
          itemCount: _channels.length + (_hasMore ? 1 : 0),
          physics: scrollPhysics,
          // Performance optimizations
          cacheExtent: 500.0, // Cache more items for smoother scrolling
          addAutomaticKeepAlives: false, // Don't keep items alive unnecessarily
          addRepaintBoundaries: true, // Add repaint boundaries automatically
          addSemanticIndexes: false, // Disable semantic indexes for performance
          itemBuilder: (context, index) {
            if (index == _channels.length) {
              return loadingIndicator;
            }
            final channel = _channels[index];
            return RepaintBoundary(
              child: ChannelCardWidget(
                key: ValueKey(
                  channel.channelId,
                ), // Add key for better performance
                channel: channel,
                mode: _displayMode,
                onChannelTap: _handleChannelTap,
                onToggleFavorite: () async {
                  await channel.toggleFavorite();
                  // Use more efficient state update
                  if (mounted) {
                    setState(() {});
                  }
                },
              ),
            );
          },
          separatorBuilder:
              (context, index) =>
                  const SizedBox(height: 8.0), // Add spacing here
        ),
      );
    } else {
      currentView = LayoutBuilder(
        builder: (context, constraints) {
          final double screenWidth = constraints.maxWidth;
          final double itemWidth = 150.0; // Desired width of each grid item
          final double itemHeight = 150.0; // Desired height of each grid item

          final int crossAxisCount = (screenWidth / itemWidth).floor().clamp(
            1,
            5,
          ); // Max 5 columns
          final double crossAxisSpacing = 8.0;
          final double mainAxisSpacing = 8.0;

          // Calculate the total width taken by items and spacing
          final double totalItemWidth =
              (crossAxisCount * itemWidth) +
              ((crossAxisCount - 1) * crossAxisSpacing);

          // Calculate the remaining space and distribute it as padding or adjust item width
          // For simplicity, we'll adjust childAspectRatio to fit the available width
          final double effectiveItemWidth =
              (screenWidth - (crossAxisSpacing * (crossAxisCount - 1))) /
              crossAxisCount;
          final double childAspectRatio = effectiveItemWidth / itemHeight;

          return ScrollConfiguration(
            behavior: ScrollConfiguration.of(context).copyWith(
              scrollbars: false,
              overscroll: false, // Disable glow/overscroll on mobile
            ),
            child: GridView.builder(
              key: const ValueKey('gridView'), // Key for AnimatedSwitcher
              controller: _scrollController,
              padding: const EdgeInsets.all(8.0),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: crossAxisSpacing,
                mainAxisSpacing: mainAxisSpacing,
                childAspectRatio: childAspectRatio,
              ),
              itemCount: _channels.length + (_hasMore ? 1 : 0),
              physics: scrollPhysics,
              // Performance optimizations
              cacheExtent: 800.0, // Cache more items for grid view
              addAutomaticKeepAlives: false,
              addRepaintBoundaries: true,
              addSemanticIndexes: false,
              itemBuilder: (context, index) {
                if (index == _channels.length) {
                  return loadingIndicator;
                }
                final channel = _channels[index];
                return RepaintBoundary(
                  child: ChannelCardWidget(
                    key: ValueKey(channel.channelId),
                    channel: channel,
                    mode: _displayMode,
                    onChannelTap: _handleChannelTap,
                    onToggleFavorite: () async {
                      await channel.toggleFavorite();
                      // Use more efficient state update
                      if (mounted) {
                        setState(() {});
                      }
                    },
                  ),
                );
              },
            ),
          );
        },
      );
    }

    return ScrollConfiguration(
      behavior: MobileOptimizedScrollBehavior(),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 150), // Reduced duration
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: child, // Removed ScaleTransition
          );
        },
        child: currentView,
      ),
    );
  }

  Future<void> _handleChannelTap(BuildContext context, Channel channel) async {
    if (kDebugMode) {
      print('Fetching sources for channel: ${channel.channelId}');
    }
    final sources = await _repo.getChannelSources(channel.channelId);
    if (kDebugMode) {
      print('Channel sources for ${channel.channelId}: $sources');
    }
    if (sources.isNotEmpty) {
      final isExternal = await _repo.isChannelSourceExternal(channel.channelId);
      if (!mounted) return;

      final shouldShowAd = await _adManager.shouldShowAd();

      if (shouldShowAd) {
        await _adManager.recordAdDisplay();
        final adUrl = await _adManager.getAdUrl();
        if (!mounted) return; // Add mounted check here
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) => AdPage(
                  url: adUrl,
                  refererUrl: 'https://cat-tv.live/',
                  channel: channel,
                  channelSources: sources, // Pass the list of maps
                  isExternal: isExternal,
                ),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      } else {
        // Proceed to player or webview
        if (isExternal) {
          if (!mounted) return; // Add mounted check here
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder:
                  (context, animation, secondaryAnimation) => WebViewPage(
                    channel: channel,
                    channelUrl: sources.first['source_url'],
                  ), // Pass only the URL
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                return FadeTransition(opacity: animation, child: child);
              },
            ),
          );
        } else {
          if (!mounted) return; // Add mounted check here
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder:
                  (context, animation, secondaryAnimation) => PlayerPage(
                    channel: channel,
                    channelSources: sources,
                  ), // Pass the list of maps
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                return FadeTransition(opacity: animation, child: child);
              },
            ),
          );
        }
      }
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.noSourcesAvailable),
        ),
      );
    }
  }

  Future<void> _loadIptvStatusAndExpiration() async {
    _currentExpirationDate = await _iptvDataService.getExpirationTime();
    setState(() {}); // Update UI if needed
  }

  Future<void> _triggerIptvUpdate(String initialStatusMessage) async {
    setState(() {
      _isIptvUpdating = true;
      _iptvUpdateStatus = initialStatusMessage;
    });

    final iptvFetcher = IptvWebViewFetcher();
    try {
      final result = await Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) =>
                  IptvWebViewPage(fetcher: iptvFetcher),
          opaque: false, // Make the route opaque to hide the content
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      );

      if (result != null && result is Map<String, dynamic>) {
        if (result.containsKey("error")) {
          setState(() {
            _iptvUpdateStatus =
                AppLocalizations.of(context)!.failedToUpdateServers;
          });
          if (kDebugMode) {
            print('IPTV Data extraction failed: ${result["error"]}');
          }
        } else {
          // Successfully updated, reload last update date
          await _loadIptvStatusAndExpiration(); // Corrected method call
          setState(() {
            _iptvUpdateStatus =
                AppLocalizations.of(context)!.serversUpdateSuccess;
          });
          if (kDebugMode) {
            print('IPTV Data extraction successful.');
          }
        }
      } else {
        setState(() {
          _iptvUpdateStatus =
              AppLocalizations.of(context)!.serversUpdateCancelled;
        });
        if (kDebugMode) {
          print('IPTV Data extraction cancelled or failed.');
        }
      }
    } catch (e) {
      setState(() {
        _iptvUpdateStatus = AppLocalizations.of(context)!.failedToUpdateServers;
      });
      if (kDebugMode) {
        print('Error during IPTV data fetching: $e');
      }
    } finally {
      // Keep the status message visible for a short duration
      await Future.delayed(const Duration(seconds: 3));
      setState(() {
        _isIptvUpdating = false;
        _iptvUpdateStatus = '';
      });
    }
  }

  void _showTemporaryStatus(String message) {
    setState(() {
      _isIptvUpdating = true;
      _iptvUpdateStatus = message;
    });
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isIptvUpdating = false;
          _iptvUpdateStatus = '';
        });
      }
    });
  }

  List<Widget> _getWidgetOptions() {
    return <Widget>[
      CustomScrollView(
        controller: _scrollController, // Use the same scroll controller
        slivers: [
          SliverAppBar(
            // Main App Title AppBar
            title: const Text(
              'Cat TV', // Hardcoded to prevent translation
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
            backgroundColor: Colors.transparent, // Transparent app bar
            elevation: 0,
            centerTitle: true,
            flexibleSpace: ClipRRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color.fromARGB(255, 50, 0, 50), // Dark purple
                        Color.fromARGB(255, 20, 20, 20), // Dark grey
                      ],
                    ),
                    border: Border(
                      bottom: BorderSide(color: Colors.white10, width: 0.5),
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.web), // Icon for webview
                onPressed: () async {
                  await _loadIptvStatusAndExpiration(); // Ensure latest expiration date is loaded

                  // If _currentExpirationDate is null (deleted/never set) OR
                  // if current time is AFTER the expiration date, then trigger update.
                  if (_currentExpirationDate == null ||
                      DateTime.now().isAfter(_currentExpirationDate!)) {
                    await _triggerIptvUpdate(
                      AppLocalizations.of(context)!.updatingServersData,
                    );
                  } else {
                    // If _currentExpirationDate exists and current time is BEFORE it, show up to date.
                    _showTemporaryStatus(
                      AppLocalizations.of(context)!.alreadyUpToDate,
                    );
                    if (kDebugMode) {
                      print('IPTV data is still valid, no update needed.');
                    }
                  }
                },
                tooltip: 'IPTV Account Creator',
              ),
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsPage(),
                    ),
                  );
                },
                tooltip: 'Settings',
              ),
            ],
            pinned: true, // Make it sticky
            expandedHeight: kToolbarHeight + MediaQuery.of(context).padding.top,
          ),
          SliverAppBar(
            // Filter AppBar (now only contains the FilterWidget in its bottom)
            toolbarHeight: 0.0, // Set to 0 as it won't have a title row
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: ClipRRect(
              // Apply the same blur/gradient as main AppBar
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color.fromARGB(255, 50, 0, 50), // Dark purple
                        Color.fromARGB(255, 20, 20, 20), // Dark grey
                      ],
                    ),
                    border: Border(
                      bottom: BorderSide(color: Colors.white10, width: 0.5),
                    ),
                  ),
                ),
              ),
            ),
            // Removed the title: Padding(...) here
            bottom: PreferredSize(
              // This will be the FilterWidget
              preferredSize: Size.fromHeight(_showFilters ? 200.0 : 0.0),
              child: AnimatedCrossFade(
                duration: const Duration(milliseconds: 300),
                crossFadeState:
                    _showFilters
                        ? CrossFadeState.showFirst
                        : CrossFadeState.showSecond,
                firstChild: FilterWidget(
                  controller: _filterController,
                  regions: _regions,
                  countries: _countries,
                  languages: _languages,
                  categories: _categories,
                ),
                secondChild: const SizedBox.shrink(),
              ),
            ),
            pinned: true, // Make it sticky
            expandedHeight: _filterAppBarHeight,
          ),
          SliverToBoxAdapter(
            child: Padding(
              // New Padding for the moved buttons
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 4.0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SegmentedButton<DisplayMode>(
                    segments: const <ButtonSegment<DisplayMode>>[
                      ButtonSegment<DisplayMode>(
                        value: DisplayMode.list,
                        icon: Icon(Icons.list_rounded),
                      ),
                      ButtonSegment<DisplayMode>(
                        value: DisplayMode.smallTiles,
                        icon: Icon(Icons.apps_rounded),
                      ),
                    ],
                    selected: <DisplayMode>{_displayMode},
                    onSelectionChanged: (Set<DisplayMode> newSelection) {
                      setState(() {
                        _displayMode = newSelection.first;
                      });
                    },
                    showSelectedIcon: false, // Remove the checkmark
                    style: SegmentedButton.styleFrom(
                      foregroundColor: Colors.white,
                      selectedForegroundColor: Colors.deepPurpleAccent,
                      selectedBackgroundColor:
                          Colors.transparent, // Make background transparent
                      side: const BorderSide(
                        color: Colors.white54,
                      ), // Keep border for separation
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  ),
                  if (kIsWeb ||
                      Platform.isWindows ||
                      Platform.isLinux ||
                      Platform.isMacOS)
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _showFilters = !_showFilters;
                        });
                      },
                      icon: Icon(
                        _showFilters ? Icons.filter_alt_off : Icons.filter_alt,
                        color: Colors.white,
                      ),
                      label: Text(
                        _showFilters
                            ? AppLocalizations.of(context)!.hideFilters
                            : AppLocalizations.of(context)!.showFilters,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                ],
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: const SizedBox(
              height: 8.0,
            ), // Add spacing between filter row and display mode
          ),
          SliverFillRemaining(
            child: Row(children: [Expanded(child: _buildChannelList())]),
          ),
        ],
      ),
      const FixturesListPage(),
      const FavoritesPage(), // Use the FavoritesPage here
    ];
  }

  void _onItemTapped(int index) async {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final widgetOptions = _getWidgetOptions();
    return Scaffold(
      extendBodyBehindAppBar:
          true, // Extend body behind app bar for glassmorphism
      backgroundColor: Colors.black, // Dark background for the app
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 20, 20, 20),
                  Color.fromARGB(255, 50, 0, 50),
                ],
              ),
            ),
            child: widgetOptions.elementAt(_selectedIndex),
          ),
          if (_isIptvUpdating)
            Container(
              color: Colors.black54, // Semi-transparent overlay
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 20),
                    Text(
                      _iptvUpdateStatus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: BottomNavigationBar(
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon:
                    _selectedIndex == 0
                        ? const Icon(Icons.live_tv_rounded, size: 30)
                        : const Icon(Icons.live_tv_outlined, size: 28),
                label: AppLocalizations.of(context)!.liveTab,
              ),
              BottomNavigationBarItem(
                icon:
                    _selectedIndex == 1
                        ? const Icon(Icons.sports_soccer_rounded, size: 30)
                        : const Icon(Icons.sports_soccer_outlined, size: 28),
                label: AppLocalizations.of(context)!.fixturesTab,
              ),
              BottomNavigationBarItem(
                icon:
                    _selectedIndex == 2
                        ? const Icon(Icons.favorite_rounded, size: 30)
                        : const Icon(Icons.favorite_border_rounded, size: 28),
                label: AppLocalizations.of(context)!.favoritesTab,
              ),
            ],
            currentIndex: _selectedIndex,
            selectedItemColor: Colors.deepPurpleAccent,
            unselectedItemColor: Colors.white70,
            onTap: _onItemTapped,
            backgroundColor: Colors.black.withValues(alpha: 0.4),
            type: BottomNavigationBarType.fixed,
            selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}

class ChannelPage extends StatefulWidget {
  const ChannelPage({super.key});

  @override
  State<ChannelPage> createState() => _ChannelPageState();
}

class _ChannelPageState extends State<ChannelPage> {
  final filter_ctrl.FilterController _filterController =
      filter_ctrl.FilterController();
  List<Region> _regions = [];
  List<Country> _countries = [];
  List<Language> _languages = [];
  List<cat_model.Category> _categories = [];

  @override
  void initState() {
    super.initState();
    _initFilterData();
    _filterController.addListener(() {
      debugPrint('[ChannelPage] Filter changed: ${_filterController.filter}');
      loadChannels();
    });
  }

  Future<void> _initFilterData() async {
    final db = await DatabaseLoader.openPrebuiltDatabase();
    final regionRepo = RegionRepository(db);
    final countryRepo = CountryRepository(db);
    final languageRepo = LanguageRepository(db);
    final categoryRepo = CategoryRepository(db);
    final regions = await regionRepo.getAllRegions();
    final countries = await countryRepo.getAllCountries();
    final languages = await languageRepo.getAllLanguages();
    final categories = await categoryRepo.getAllCategories();
    setState(() {
      _regions = regions;
      _countries = countries;
      _languages = languages;
      _categories = categories.cast<cat_model.Category>();
    });
  }

  void loadChannels() {
    final filter = _filterController.filter;
    debugPrint('Loading with filter: $filter');
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(AppLocalizations.of(context)!.channelsTitle)),
      body: Column(
        children: [
          FilterWidget(
            controller: _filterController,
            regions: _regions,
            countries: _countries,
            languages: _languages,
            categories: _categories,
          ),
          Expanded(
            child: Center(
              child: Text(AppLocalizations.of(context)!.channelListPlaceholder),
            ),
          ),
        ],
      ),
    );
  }
}

class Category {
  final int id;
  final String name;
  Category({required this.id, required this.name});

  factory Category.fromMap(Map<String, dynamic> map) {
    final idValue = map['id'];
    if (idValue == null) {
      if (kDebugMode) {
        print('Warning: Category row with null id: $map');
      }
      return Category(id: -1, name: map['name'] as String? ?? 'Unknown');
    }
    return Category(
      id: idValue is int ? idValue : int.tryParse(idValue.toString()) ?? -1,
      name: map['name'] as String? ?? 'Unknown',
    );
  }
}
